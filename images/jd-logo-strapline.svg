<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 16.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
	<!ENTITY ns_extend "http://ns.adobe.com/Extensibility/1.0/">
	<!ENTITY ns_ai "http://ns.adobe.com/AdobeIllustrator/10.0/">
	<!ENTITY ns_graphs "http://ns.adobe.com/Graphs/1.0/">
	<!ENTITY ns_vars "http://ns.adobe.com/Variables/1.0/">
	<!ENTITY ns_imrep "http://ns.adobe.com/ImageReplacement/1.0/">
	<!ENTITY ns_sfw "http://ns.adobe.com/SaveForWeb/1.0/">
	<!ENTITY ns_custom "http://ns.adobe.com/GenericCustomNamespace/1.0/">
	<!ENTITY ns_adobe_xpath "http://ns.adobe.com/XPath/1.0/">
]>
<svg version="1.1" id="Layer_1" xmlns:x="&ns_extend;" xmlns:i="&ns_ai;" xmlns:graph="&ns_graphs;"
	 xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="463px" height="151px"
	 viewBox="0 0 463 151" enable-background="new 0 0 463 151" xml:space="preserve">
<metadata>
	<sfw  xmlns="&ns_sfw;">
		<slices></slices>
		<sliceSourceBounds  width="467.28" height="174.96" x="116.813" y="-388.039" bottomLeftOrigin="true"></sliceSourceBounds>
	</sfw>
</metadata>
<g>
	
		<image overflow="visible" width="1947" height="729" xlink:href="D23A9B28F5A5D5F7.png"  transform="matrix(0.24 0 0 0.24 -0.187 -8.9209)">
	</image>
</g>
<g>
	<path fill="#FFFFFF" d="M47.396,59.548c0-0.342-0.025-2.722-0.075-7.147c-0.05-4.424-0.071-7.585-0.071-9.489V32.169l0.229-6.47
		c-0.035-0.275-0.119-0.535-0.26-0.759l-0.127-0.178c-0.297-0.364-0.729-0.605-1.216-0.662l-0.237,0.026
		c-0.123,0-0.318,0.022-0.586,0.074c-0.195,0-0.376,0-0.548,0h-0.623l-1.23-0.114c-0.762,0.011-1.411,0.495-1.672,1.174
		c-0.019,0.102-0.032,0.222-0.04,0.388c0.033,0.935,0.08,2.257,0.146,3.981c0.134,3.617,0.202,7.011,0.202,10.177
		c0,2.562,0.011,5.253,0.037,8.08c0.021,2.829,0.055,5.701,0.089,8.628c0.04,2.925,0.057,5.982,0.057,9.178
		c0,6.774-0.688,11.28-2.068,13.521c-1.376,2.241-3.908,3.595-7.588,4.056l0.439,2.192c5.483-0.583,9.404-2.219,11.757-4.91
		c2.353-2.693,3.53-6.909,3.53-12.631L47.396,59.548z"/>
	<path fill="#FFFFFF" d="M88.292,75.835c-8.488,0-15.183-2.358-20.083-7.074c-4.9-4.719-7.348-11.129-7.348-19.231
		c0-7.983,2.492-14.373,7.475-19.177c4.984-4.802,11.636-7.202,19.956-7.202c8.418,0,15.077,2.366,19.975,7.09
		c4.9,4.733,7.348,11.16,7.348,19.289c0,7.837-2.51,14.177-7.529,19.031C103.066,73.41,96.468,75.835,88.292,75.835z M88.292,73.004
		c6.361,0,11.274-2.036,14.732-6.114c3.459-4.075,5.187-9.886,5.187-17.436c0-7.715-1.69-13.562-5.078-17.542
		c-3.387-3.98-8.333-5.968-14.841-5.968c-6.385,0-11.314,2.074-14.784,6.224C70.036,36.316,68.3,42.178,68.3,49.75
		c0,7.474,1.723,13.219,5.171,17.233C76.917,70.998,81.856,73.004,88.292,73.004z"/>
	<path fill="#FFFFFF" d="M129.053,74.783c0-0.92,0.047-2.153,0.147-3.701c0.214-3.506,0.325-5.916,0.325-7.221l0.146-15.022
		c0-2.539-0.018-5.116-0.055-7.728c-0.037-2.611-0.068-4.729-0.091-6.351c0-1.209-0.049-2.637-0.147-4.28
		c-0.216-3.681-0.325-5.831-0.325-6.461l3.375,0.146h5.769h8.999l8.017-0.146l-0.036,1.056l-0.035,1.122l0.035,1.053l0.036,1.086
		l-8.667-0.543c-1.358,0-2.464-0.011-3.312-0.035c-3.401-0.096-5.293-0.143-5.682-0.143h-1.785c0,0.965,0,2.367,0,4.206
		c0,1.839,0,3.18,0,4.029v7.437v2.975h1.306c0.773,0,2.126-0.024,4.063-0.071c1.937-0.05,3.061-0.076,3.375-0.076l8.708-0.432
		l-0.109,1.306l-0.037,0.979l0.037,0.869l0.109,1.199c-0.604-0.12-1.294-0.223-2.068-0.31c-0.774-0.085-1.575-0.126-2.393-0.126
		c-0.342,0-1.575-0.024-3.7-0.072c-2.132-0.048-4.09-0.075-5.881-0.075h-3.41v3.557v10.345c0,0.844,0,2.138,0,3.88
		c0,1.739,0,3.045,0,3.917h1.857c1.068,0,1.916-0.009,2.549-0.037c3.108-0.12,4.868-0.181,5.28-0.181
		c0.997,0,2.172-0.059,3.534-0.18c3.277-0.291,5.451-0.435,6.518-0.435l-0.037,1.122l-0.035,1.124l0.035,1.127l0.037,1.088
		l-4.717-0.109l-7.983-0.035l-8.382,0.035L129.053,74.783z"/>
	<path fill="#FFFFFF" d="M253.444,74.783c0-0.92,0.048-2.153,0.142-3.701c0.221-3.506,0.33-5.916,0.33-7.221l0.144-15.022
		c0-2.539-0.019-5.116-0.054-7.728c-0.036-2.611-0.068-4.729-0.09-6.351c0-1.209-0.048-2.637-0.147-4.28
		c-0.218-3.681-0.324-5.831-0.324-6.461l3.373,0.146h5.774h8.992l8.018-0.146l-0.032,1.056l-0.035,1.122l0.035,1.053l0.032,1.086
		l-8.662-0.543c-1.359,0-2.468-0.011-3.318-0.035c-3.398-0.096-5.292-0.143-5.68-0.143h-1.782c0,0.965,0,2.367,0,4.206
		c0,1.839,0,3.18,0,4.029v7.437v2.975h1.304c0.776,0,2.128-0.024,4.065-0.071c1.933-0.05,3.055-0.076,3.372-0.076l8.71-0.432
		l-0.109,1.306l-0.036,0.979l0.036,0.869l0.109,1.199c-0.605-0.12-1.295-0.223-2.067-0.31c-0.776-0.085-1.574-0.126-2.398-0.126
		c-0.339,0-1.57-0.024-3.7-0.072c-2.131-0.048-4.087-0.075-5.879-0.075h-3.407v3.557v10.345c0,0.844,0,2.138,0,3.88
		c0,1.739,0,3.045,0,3.917h1.856c1.071,0,1.917-0.009,2.548-0.037c3.106-0.12,4.87-0.181,5.28-0.181c0.997,0,2.177-0.059,3.533-0.18
		c3.28-0.291,5.45-0.435,6.521-0.435l-0.038,1.122l-0.032,1.124l0.032,1.127l0.038,1.088l-4.719-0.109l-7.982-0.035l-8.38,0.035
		L253.444,74.783z"/>
	<path fill="#FFFFFF" d="M293.442,74.748c0-1.864,0.051-4.035,0.161-6.514c0.109-2.478,0.23-5.12,0.365-7.908
		c0.134-2.796,0.199-4.966,0.199-6.511c0-3.27,0.022-5.954,0.055-8.059c0.038-2.103,0.08-4.648,0.128-7.636s0.074-5.375,0.074-7.165
		v-7.875h1.378c2.514,2.924,5.222,6,8.124,9.217c2.759,3.071,5.613,6.19,8.565,9.359c3.071,3.293,6.168,6.543,9.291,9.762
		c3.288,3.409,6.796,6.987,10.521,10.74l0.035-1.128l0.107-5.407c0-2.731-0.009-5.73-0.04-8.995
		c-0.026-3.265-0.049-5.938-0.067-8.021c0-2.005-0.039-3.942-0.118-5.804c-0.234-5.588-0.354-8.502-0.354-8.747l1.452,0.223
		l0.907,0.034l0.942-0.034l1.378-0.223l-0.471,17.154l-0.144,17.659v6.996l0.144,9.862h-0.942c-1.064-1.33-2.283-2.792-3.646-4.39
		c-1.369-1.59-2.853-3.27-4.449-5.035c-1.013-1.162-2.103-2.369-3.264-3.624c-0.994-1.092-2.199-2.393-3.632-3.917
		c-1.689-1.811-3.032-3.242-4.023-4.277l-17.997-18.927l-0.07,4.246l-0.147,5.48c0,1.016,0.022,2.982,0.07,5.895
		c0.051,2.916,0.077,4.823,0.077,5.714l0.433,17.89l-1.309-0.254l-1.198-0.109l-1.196,0.109L293.442,74.748z"/>
	<path fill="#FFFFFF" d="M353.217,74.783l0.324-12.808l0.109-12.52l-0.109-13.132c0-1.305-0.035-2.845-0.109-4.605
		c-0.17-4.115-0.253-6.679-0.253-7.697l1.671,0.22l1.85,0.072h0.688c0.167,0,0.35,0,0.545,0c0.244-0.046,0.42-0.072,0.542-0.072
		l1.635-0.22l-0.471,13.958l-0.146,12.463c0,1.452,0.011,3.108,0.033,4.959c0.025,1.854,0.061,3.896,0.112,6.124l0.252,9.85h1.777
		h3.266c0.946,0,2.396-0.062,4.354-0.183c1.961-0.12,3.389-0.183,4.282-0.183c0.965,0,1.85-0.045,2.65-0.138
		c0.8-0.095,2.091-0.324,3.884-0.696l-0.04,1.104l-0.034,1.182l0.034,1.105v1.217l-10.848-0.218h-6.351c-0.579,0-1.439,0-2.574,0
		c-1.138,0-1.974,0-2.503,0c-0.536,0-1.423,0.037-2.667,0.109C353.874,74.748,353.242,74.783,353.217,74.783z"/>
	<path fill="#FFFFFF" d="M403.666,74.783l0.324-5.696l0.038-4.934l-0.038-4.826c0-0.656-0.039-1.603-0.125-2.848
		c-0.087-1.247-0.129-2.072-0.129-2.486c-0.192-0.363-1.323-2.298-3.385-5.807c-1.071-1.812-2.13-3.625-3.174-5.441
		c-0.872-1.499-1.879-3.191-3.023-5.077l-8.089-13.646l1.896,0.22l2.181,0.072h0.836c0.218,0,0.445,0,0.689,0
		c0.292-0.046,0.509-0.072,0.656-0.072l1.928-0.22c1.041,2.348,2.062,4.533,3.051,6.55c0.998,2.02,2.113,4.167,3.348,6.441
		c2.331,4.163,4.84,8.222,7.526,12.193c2.533-4.137,4.922-8.311,7.165-12.518c1.017-1.936,2.078-4.043,3.187-6.315
		c0.914-1.863,1.905-3.98,2.966-6.351l1.34,0.22l1.414,0.072h0.538c0.222,0,0.521-0.024,0.907-0.072l1.302-0.22
		c-0.897,1.452-1.916,3.124-3.058,5.009c-1.633,2.688-2.626,4.328-2.988,4.934c-0.974,1.622-1.93,3.232-2.865,4.827
		c-0.931,1.596-1.851,3.183-2.747,4.75c-0.63,1.066-1.521,2.614-2.662,4.645c-0.875,1.55-1.751,3.089-2.622,4.608v4.827v3.048
		c0,0.678,0,1.755,0,3.231c0,1.473,0,2.625,0,3.444l0.288,7.439l-1.67-0.254l-1.596-0.109l-1.63,0.109
		c-0.29,0-0.637,0.037-1.034,0.11C404.009,74.711,403.759,74.761,403.666,74.783z"/>
	<g>
		<path fill="#FFFFFF" d="M90.053,125.194h-1.89v-15.207h-4.094v-1.754H94.19v1.754h-4.138V125.194z"/>
		<path fill="#FFFFFF" d="M101.745,108.233h8.795v1.754h-6.906v5.04h6.704v1.753h-6.704v6.657h6.906v1.757h-8.795V108.233z"/>
		<path fill="#FFFFFF" d="M127.069,111.539c-0.675-1.169-1.485-1.843-2.924-1.843c-1.507,0-2.81,1.079-2.81,2.631
			c0,1.463,1.506,2.137,2.653,2.655l1.125,0.493c2.204,0.968,4.071,2.069,4.071,4.769c0,2.972-2.385,5.238-5.331,5.238
			c-2.722,0-4.747-1.75-5.287-4.386l1.845-0.516c0.247,1.734,1.575,3.151,3.396,3.151s3.486-1.395,3.486-3.309
			c0-1.979-1.553-2.655-3.126-3.372l-1.034-0.449c-1.98-0.901-3.689-1.912-3.689-4.343c0-2.634,2.225-4.318,4.745-4.318
			c1.89,0,3.486,0.968,4.386,2.629L127.069,111.539z"/>
		<path fill="#FFFFFF" d="M142.069,125.194h-1.89v-15.207h-4.093v-1.754h10.121v1.754h-4.139V125.194z"/>
		<path fill="#FFFFFF" d="M155.696,125.194h-1.889v-16.961h1.889V125.194z"/>
		<path fill="#FFFFFF" d="M166.964,107.268l6.162,14.235l6.187-14.235l3.44,17.927h-1.934l-2.182-11.473h-0.045l-5.467,12.168
			l-5.442-12.168h-0.045l-2.182,11.473h-1.935L166.964,107.268z"/>
		<path fill="#FFFFFF" d="M207.355,116.735c0,4.881-4.003,8.747-8.839,8.747s-8.839-3.866-8.839-8.747
			c0-4.858,4.003-8.794,8.839-8.794S207.355,111.877,207.355,116.735z M191.566,116.713c0,3.823,3.104,7.02,6.95,7.02
			c3.847,0,6.951-3.196,6.951-7.02c0-3.848-3.06-7.017-6.951-7.017C194.624,109.696,191.566,112.865,191.566,116.713z"/>
		<path fill="#FFFFFF" d="M215.652,107.514l13.226,13.855v-13.136h1.89v17.723l-13.226-13.833v13.071h-1.889V107.514z"/>
		<path fill="#FFFFFF" d="M241.852,125.194h-1.891v-16.961h1.891V125.194z"/>
		<path fill="#FFFFFF" d="M253.457,120.379l-2.049,4.815h-2.071l7.828-17.771l7.627,17.771h-2.094l-2-4.815H253.457z
			 M257.121,111.832l-2.901,6.793h5.738L257.121,111.832z"/>
		<path fill="#FFFFFF" d="M274.147,123.438h4.677v1.757h-6.568v-16.961h1.892V123.438z"/>
		<path fill="#FFFFFF" d="M309.048,123.438v1.757h-10.974l7.938-9.383c0.677-0.807,1.147-1.73,1.147-2.834
			c0-1.889-1.528-3.281-3.375-3.281c-2.045,0-3.373,1.506-3.417,3.486h-1.889c0.067-2.97,2.247-5.241,5.242-5.241
			c2.831,0,5.328,2.068,5.328,5.014c0,1.373-0.401,2.565-1.279,3.58l-5.87,6.902H309.048z"/>
		<path fill="#FFFFFF" d="M329.626,116.735c0,4.185-2.203,8.747-6.072,8.747s-6.075-4.562-6.075-8.747
			c0-3.485,1.779-8.794,6.075-8.794S329.626,113.25,329.626,116.735z M319.37,116.825c0.041,2.384,1.058,6.907,4.184,6.907
			c3.125,0,4.139-4.523,4.184-6.907c0.045-2.407-0.99-7.129-4.184-7.129C320.357,109.696,319.325,114.418,319.37,116.825z"/>
		<path fill="#FFFFFF" d="M339.656,109.987l0.99-1.754h3.712v16.961h-1.892v-15.207H339.656z"/>
		<path fill="#FFFFFF" d="M359.674,124.518l3.911-5.377l-0.045-0.045c-0.449,0.136-0.942,0.226-1.463,0.226
			c-3.106,0-5.487-2.519-5.487-5.6c0-3.216,2.655-5.78,5.847-5.78c3.193,0,5.852,2.564,5.852,5.78c0,1.529-0.699,2.858-1.574,4.069
			l-5.624,7.691L359.674,124.518z M358.478,113.657c0,2.199,1.757,3.91,3.959,3.91c2.184,0,3.962-1.711,3.962-3.91
			c0-2.186-1.778-3.961-3.962-3.961C360.234,109.696,358.478,111.472,358.478,113.657z"/>
	</g>
	<path fill="#FFFFFF" d="M237.226,35.705c-1.654-3.531-3.998-6.312-7.021-8.347c-1.887-1.28-4.043-2.193-6.476-2.737
		c-2.432-0.546-5.801-0.815-10.105-0.815l-8.129,0.181c-0.87,0-2.051,0.03-3.537,0.088c-1.487,0.062-2.401,0.091-2.739,0.091
		l-2.503-0.036l-1.314-0.048c-0.759,0.04-1.389,0.362-1.689,1.211c0.035,0.909,0.088,2.302,0.158,4.208
		c0.034,0.896,0.051,2.116,0.051,3.663c0,2.928,0,5.75,0,8.473c0,2.724,0,5.327,0,7.818v15.969l-0.291,9.359
		c0.242,0,0.733-0.022,1.469-0.072c0.739-0.048,1.351-0.072,1.833-0.072h2.647l5.515,0.144l5.334,0.147
		c9.53,0,16.793-2.305,21.79-6.913c4.997-4.608,7.491-11.316,7.491-20.121C239.71,43.3,238.883,39.235,237.226,35.705z
		 M226.469,66.346c-4.015,3.74-10.124,5.607-18.323,5.607h-3.52l-4.281-0.288c0-0.683-0.043-2.334-0.128-4.966
		c-0.084-2.625-0.128-4.729-0.128-6.303c0-1.262,0-3.32,0-6.176c0-2.862,0-5.091,0-6.69V36.847l0.256-9.702
		c0.337,0,1.124-0.045,2.358-0.13c1.232-0.085,2.214-0.125,2.938-0.125l5.842-0.108c7.449,0,12.815,1.763,16.091,5.297
		c3.28,3.531,4.918,9.274,4.918,17.234C232.492,56.93,230.483,62.611,226.469,66.346z"/>
</g>
</svg>
