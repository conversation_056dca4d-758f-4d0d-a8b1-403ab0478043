/*-----------------------------------------------------------------------------

- Revolution Slider 5.0 Navigatin Skin Style  -

 ARES SKIN

author:  ThemePunch
email:      <EMAIL>
website:    http://www.themepunch.com
-----------------------------------------------------------------------------*/
.ares.tparrows {
  cursor:pointer;
  background:#fff;
  min-width:60px;
    min-height:60px;
  position:absolute;
  display:block;
  z-index:100;
    border-radius:50%;
}
.ares.tparrows:hover {
}
.ares.tparrows:before {
  font-family: "revicons";
  font-size:25px;
  color:#aaa;
  display:block;
  line-height: 60px;
  text-align: center;
    -webkit-transition: color 0.3s;
    -moz-transition: color 0.3s;
    transition: color 0.3s;
    z-index:2;
    position:relative;
}
.ares.tparrows.tp-leftarrow:before {
  content: "\e81f";
}
.ares.tparrows.tp-rightarrow:before {
  content: "\e81e";
}
.ares.tparrows:hover:before {
 color:#000;
      }
.tp-title-wrap { 
  position:absolute;
  z-index:1;
  display:inline-block;
  background:#fff;
  min-height:60px;
  line-height:60px;
  top:0px;
  margin-left:30px;
  border-radius:0px 30px 30px 0px;
  overflow:hidden;
  -webkit-transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
  transform:scaleX(0);  
  -webkit-transform:scaleX(0);  
  transform-origin:0% 50%; 
   -webkit-transform-origin:0% 50%;
}
 .ares.tp-rightarrow .tp-title-wrap { 
   right:0px;
   margin-right:30px;margin-left:0px;
   -webkit-transform-origin:100% 50%;
border-radius:30px 0px 0px 30px;
 }
.ares.tparrows:hover .tp-title-wrap {
  transform:scaleX(1) scaleY(1);
    -webkit-transform:scaleX(1) scaleY(1);
}
.ares .tp-arr-titleholder {
  position:relative;
  -webkit-transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
  transform:translateX(200px);  
  text-transform:uppercase;
  color:#000;
  font-weight:400;
  font-size:14px;
  line-height:60px;
  white-space:nowrap;
  padding:0px 20px;
  margin-left:10px;
  opacity:0;
}

.ares.tp-rightarrow .tp-arr-titleholder {
   transform:translateX(-200px); 
   margin-left:0px; margin-right:10px;
      }

.ares.tparrows:hover .tp-arr-titleholder {
   transform:translateX(0px);
   -webkit-transform:translateX(0px);
  transition-delay: 0.1s;
  opacity:1;
}

/* BULLETS */
.ares.tp-bullets {
}
.ares.tp-bullets:before {
	content:" ";
	position:absolute;
	width:100%;
	height:100%;
	background:transparent;
	padding:10px;
	margin-left:-10px;margin-top:-10px;
	box-sizing:content-box;
}
.ares .tp-bullet {
	width:13px;
	height:13px;
	position:absolute;
	background:#e5e5e5;
	border-radius:50%;
	cursor: pointer;
	box-sizing:content-box;
}
.ares .tp-bullet:hover,
.ares .tp-bullet.selected {
	background:#fff;
}
.ares .tp-bullet-title {
  position:absolute;
  color:#888;
  font-size:12px;
  padding:0px 10px;
  font-weight:600;
  right:27px;
  top:-4px;
  background:#fff;
  background:rgba(255,255,255,0.75);
  visibility:hidden;
  transform:translateX(-20px);
  -webkit-transform:translateX(-20px);
  transition:transform 0.3s;
  -webkit-transition:transform 0.3s;
  line-height:20px;
  white-space:nowrap;
}     

.ares .tp-bullet-title:after {
    width: 0px;
	height: 0px;
	border-style: solid;
	border-width: 10px 0 10px 10px;
	border-color: transparent transparent transparent rgba(255,255,255,0.75);
	content:" ";
    position:absolute;
    right:-10px;
	top:0px;
}
    
.ares .tp-bullet:hover .tp-bullet-title{
  visibility:visible;
   transform:translateX(0px);
  -webkit-transform:translateX(0px);
}

.ares .tp-bullet.selected:hover .tp-bullet-title {
    background:#fff;
        }
.ares .tp-bullet.selected:hover .tp-bullet-title:after {
  border-color:transparent transparent transparent #fff;
}
.ares.tp-bullets:hover .tp-bullet-title {
        visibility:hidden;
}
.ares.tp-bullets:hover .tp-bullet:hover .tp-bullet-title {
    visibility:visible;
      }

/* TABS */
.ares .tp-tab { 
  opacity:1;      
  padding:10px;
  box-sizing:border-box;
  font-family: "Roboto", sans-serif;
  border-bottom: 1px solid #e5e5e5;
 }
.ares .tp-tab-image 
{ 
  width:60px;
  height:60px; max-height:100%; max-width:100%;
  position:relative;
  display:inline-block;
  float:left;

}
.ares .tp-tab-content 
{
    background:rgba(0,0,0,0); 
    position:relative;
    padding:15px 15px 15px 85px;
 left:0px;
 overflow:hidden;
 margin-top:-15px;
    box-sizing:border-box;
    color:#333;
    display: inline-block;
    width:100%;
    height:100%;
 position:absolute; }
.ares .tp-tab-date
  {
  display:block;
  color: #aaa;
  font-weight:500;
  font-size:12px;
  margin-bottom:0px;
  }
.ares .tp-tab-title 
{
    display:block;	
    text-align:left;
    color:#333;
    font-size:14px;
    font-weight:500;
    text-transform:none;
    line-height:17px;
}
.ares .tp-tab:hover,
.ares .tp-tab.selected {
	background:#eee; 
}

.ares .tp-tab-mask {
}

/* MEDIA QUERIES */
@media only screen and (max-width: 960px) {

}
@media only screen and (max-width: 768px) {

}

