/*!
 * bootstrap-star-rating v4.0.3
 * http://plugins.krajee.com/star-rating
 *
 * Author: <PERSON><PERSON><PERSON>
 * Copyright: 2013 - 2017, <PERSON><PERSON><PERSON>, Krajee.com
 *
 * Licensed under the BSD 3-Clause
 * https://github.com/kartik-v/bootstrap-star-rating/blob/master/LICENSE.md
 */
.rating-loading {
	width: 25px;
	height: 25px;
	font-size: 0;
	color: #fff;
	background: transparent url('../img/loading.gif') top left no-repeat;
	border: none;
}

/*
 * Stars & Input
 */
.rating-container .rating-stars {
	position: relative;
	cursor: pointer;
	vertical-align: middle;
	display: inline-block;
	overflow: hidden;
	white-space: nowrap;
}

.rating-container .rating-input {
	position: absolute;
	cursor: pointer;
	width: 100%;
	height: 1px;
	bottom: 0;
	left: 0;
	font-size: 1px;
	border: none;
	background: none;
	padding: 0;
	margin: 0;
}

.rating-disabled .rating-input, .rating-disabled .rating-stars {
	cursor: not-allowed;
}

.rating-container .star {
	display: inline-block;
	margin: 0 3px;
	text-align: center;
}

.rating-container .empty-stars {
	color: #aaa;
}

.rating-container .filled-stars {
	position: absolute;
	left: 0;
	top: 0;
	margin: auto;
	color: #fde16d;
	white-space: nowrap;
	overflow: hidden;
	-webkit-text-stroke: 1px #777;
	text-shadow: 1px 1px #999;
}

.rating-rtl {
	float: right;
}

.rating-animate .filled-stars {
	transition: width 0.25s ease;
	-o-transition: width 0.25s ease;
	-moz-transition: width 0.25s ease;
	-webkit-transition: width 0.25s ease;
}

.rating-rtl .filled-stars {
	left: auto;
	right: 0;
	-moz-transform: matrix(-1, 0, 0, 1, 0, 0) translate3d(0, 0, 0);
	-webkit-transform: matrix(-1, 0, 0, 1, 0, 0) translate3d(0, 0, 0);
	-o-transform: matrix(-1, 0, 0, 1, 0, 0) translate3d(0, 0, 0);
	transform: matrix(-1, 0, 0, 1, 0, 0) translate3d(0, 0, 0);
}

.rating-rtl.is-star .filled-stars {
	right: 0.06em;
}

.rating-rtl.is-heart .empty-stars {
	margin-right: 0.07em;
}

/**
 * Sizes
 */
.rating-xl {
	font-size: 4.89em;
}

.rating-lg {
	font-size: 3.91em;
}

.rating-md {
	font-size: 3.13em;
}

.rating-sm {
	font-size: 2.5em;
}

.rating-xs {
	font-size: 2em;
}

.rating-xl {
	font-size: 4.89em;
}

/**
 * Clear
 */
.rating-container .clear-rating {
	color: #aaa;
	cursor: not-allowed;
	display: inline-block;
	vertical-align: middle;
	font-size: 60%;
}

.clear-rating-active {
	cursor: pointer !important;
}

.clear-rating-active:hover {
	color: #843534;
}

.rating-container .clear-rating {
	padding-right: 5px;
}

/**
 * Caption
 */

/* extend support to BS4 */
.rating-container .caption .label {
	display: inline-block;
	padding: .25em .4em;
	line-height: 1;
	text-align: center;
	vertical-align: baseline;
	border-radius: .25rem;
}

.rating-container .caption {
	color: #999;
	display: inline-block;
	vertical-align: middle;
	font-size: 60%;
	margin-top: -0.6em;
}

.rating-container .caption {
	margin-left: 5px;
	margin-right: 0;
}

.rating-rtl .caption {
	margin-right: 5px;
	margin-left: 0;
}

/**
 * Print
 */
@media print {
	.rating-container .clear-rating {
		display: none;
	}
}

/*!
 * Krajee SVG Theme styling for bootstrap-star-rating.
 * This file must be loaded after 'star-rating.css'.
 *
 * @see http://github.com/kartik-v/bootstrap-star-rating
 * <AUTHOR> Visweswaran <<EMAIL>>
 */
.theme-krajee-svg .krajee-icon{display:inline-block;width:48px;height:48px;-webkit-background-size:cover;-moz-background-size:cover;-o-background-size:cover;background-size:cover}.theme-krajee-svg.rating-xl .krajee-icon{width:80px;height:80px}.theme-krajee-svg.rating-lg .krajee-icon{width:64px;height:64px}.theme-krajee-svg.rating-sm .krajee-icon{width:40px;height:40px}.theme-krajee-svg.rating-xs .krajee-icon{width:30px;height:30px}.theme-krajee-svg .filled-stars .krajee-icon-star{background-image:url('data:image/svg+xml;charset=utf-8,%3Csvg%20version%3D%221.1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20width%3D%2232%22%20height%3D%2232%22%20viewBox%3D%220%200%2032%2032%22%3E%3Cpath%20fill%3D%22%23fde16d%22%20stroke%3D%22%23777777%22%20d%3D%22M20.6%2011l-4.6-10.5-4.6%2010.5h-10.8l7.8%207.9-3%2012.1%2010.6-6%2010.6%206-3-12.1%207.8-7.9z%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E')}.theme-krajee-svg .empty-stars .krajee-icon-star{background-image:url('data:image/svg+xml;charset=utf-8,%3Csvg%20version%3D%221.1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20width%3D%2232%22%20height%3D%2232%22%20viewBox%3D%220%200%2032%2032%22%3E%3Cpath%20fill%3D%22%23ffffff%22%20stroke%3D%22%23777777%22%20d%3D%22M20.6%2011l-4.6-10.5-4.6%2010.5h-10.8l7.8%207.9-3%2012.1%2010.6-6%2010.6%206-3-12.1%207.8-7.9z%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E')}.theme-krajee-svg .filled-stars .krajee-icon-heart{background-image:url('data:image/svg+xml;charset=utf-8,%3Csvg%20version%3D%221.1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%3E%3Cpath%20fill%3D%22%23fde16d%22%20stroke%3D%22%23777777%22%20stroke-width%3D%220.04em%22%20d%3D%22M12%2021.35l-1.45-1.32c-5.15-4.67-8.55-7.75-8.55-11.53%200-3.08%202.42-5.5%205.5-5.5%201.74%200%203.41.81%204.5%202.09%201.09-1.28%202.76-2.09%204.5-2.09%203.08%200%205.5%202.42%205.5%205.5%200%203.78-3.4%206.86-8.55%2011.54l-1.45%201.31z%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E')}.theme-krajee-svg .empty-stars .krajee-icon-heart{background-image:url('data:image/svg+xml;charset=utf-8,%3Csvg%20version%3D%221.1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%3E%3Cpath%20fill%3D%22%23ffffff%22%20stroke%3D%22%23777777%22%20stroke-width%3D%220.04em%22%20d%3D%22M12%2021.35l-1.45-1.32c-5.15-4.67-8.55-7.75-8.55-11.53%200-3.08%202.42-5.5%205.5-5.5%201.74%200%203.41.81%204.5%202.09%201.09-1.28%202.76-2.09%204.5-2.09%203.08%200%205.5%202.42%205.5%205.5%200%203.78-3.4%206.86-8.55%2011.54l-1.45%201.31z%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E')}.theme-krajee-svg .krajee-icon-heart{margin:0 -.08em}.theme-krajee-svg.is-heart .caption{margin-top:-.8em}.theme-krajee-svg.is-heart .clear-rating{margin-top:-.4em}.theme-krajee-svg.rating-rtl.is-heart .filled-stars{right:.02em}.theme-krajee-svg.rating-rtl.is-heart .empty-stars{margin-right:.01em}


/*!
 * Krajee Font Awesome Theme styling for bootstrap-star-rating.
 * This file must be loaded after 'star-rating.css'.
 *
 * @see http://github.com/kartik-v/bootstrap-star-rating
 * <AUTHOR> Visweswaran <<EMAIL>>
 */.theme-krajee-fa .star{font-size:1.1em}.theme-krajee-fa .caption{margin-top:-.2em}