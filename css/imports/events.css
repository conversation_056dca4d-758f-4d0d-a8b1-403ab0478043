
/* ----------------------------------------------------------------

	events.css

-----------------------------------------------------------------*/


.events .entry,
.ievent {
	padding: 20px;
	background-color: #F9F9F9;
	border-bottom: 3px solid #EEE;
}

.events .entry:after { display: none !important; }

.events .entry-image .entry-date,
.ievent .entry-image .entry-date {
	position: absolute;
	top: 10px;
	left: 10px;
	background-color: rgba(0,0,0,0.7);
	color: #FFF;
	text-shadow: 1px 1px 1px rgba(0,0,0,0.2);
	width: 60px;
	height: 64px;
	text-align: center;
	padding-top: 9px;
	font-size: 26px;
	line-height: 1;
}

.events .entry-image .entry-date span,
.ievent .entry-image .entry-date span {
	display: block;
	font-size: 12px;
	margin-top: 7px;
}

.events .entry-c,
.ievent .entry-c { padding: 5px 0; }

.single-event .entry-image img { border-radius: 0; }

.single-event .events-meta { font-size: 14px; }

.single-event .events-meta .iconlist li,
.parallax .iconlist li { margin: 5px 0; }

.single-event .entry-overlay,
.parallax .entry-overlay-meta {
	position: absolute;
	top: auto;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 80px;
	background-color: rgba(0,0,0,0.85);
	text-align: center;
	line-height: 80px;
	font-size: 22px;
	color: #FFF;
	text-shadow: 1px 1px 1px rgba(0,0,0,0.3);
	z-index: 5;
}

.single-event .col_full .entry-overlay,
.single-event .col_three_fourth .entry-overlay { position: relative; }

.single-event .countdown {
	display: inline-block;
	position: relative;
	top: 7px;
}

.single-event .countdown-section {
	padding: 0 15px;
	border-left-color: rgba(255,255,255,0.3);
	color: #CCC;
}

.single-event .countdown-amount { color: #FFF; }

.parallax .entry-overlay,
.parallax .entry-overlay-meta {
	background: transparent;
	width: auto;
	text-align: right;
	bottom: 30px;
	left: auto;
	right: 30px;
}

.parallax .entry-overlay-meta {
	padding: 20px;
	font-size: 14px;
	text-align: left;
	right: 38px;
	bottom: 130px;
	width: 368px;
	height: auto;
	line-height: inherit;
	background-color: rgba(0,0,0,0.7);
	border-radius: 3px;
}

.parallax.overlay-left .entry-overlay,
.parallax.overlay-left .entry-overlay-meta {
	right: auto;
	left: 30px;
}

.parallax.overlay-left .entry-overlay-meta { left: 38px; }

.parallax.overlay-center .entry-overlay,
.parallax.overlay-center .entry-overlay-meta {
	bottom: 100px;
	right: auto;
	left: 50%;
	margin-left: -192px;
}

.parallax.overlay-center .entry-overlay-meta {
	top: 100px;
	bottom: auto;
	margin-left: -184px;
}

.parallax .entry-overlay-meta h2 {
	font-size: 20px;
	text-transform: uppercase;
	border-bottom: 1px dashed rgba(255,255,255,0.2);
	padding-bottom: 17px;
}

.parallax .entry-overlay-meta h2 a { color: #FFF; }

.parallax .entry-overlay-meta h2 a:hover { color: #DDD; }

.parallax .countdown { top: 0; }

.parallax .countdown-section {
	border-left: 0;
	width: 80px;
	height: 80px;
	margin: 0 8px;
	font-size: 13px;
	background-color: rgba(0,0,0,0.7);
	border-radius: 3px;
	padding-top: 14px;
}

.parallax .countdown-amount {
	font-size: 28px;
	margin-bottom: 6px;
}


/* Individual Events
-----------------------------------------------------------------*/

.ievent {
	position: relative;
	margin-top: 20px;
}

.ievent:first-child { margin-top: 0; }

.ievent .entry-image {
	width: 30%;
	margin-right: 20px;
}

.ievent .entry-content { margin-top: 20px; }

.ievent .entry-title h2 { font-size: 18px; }

.ievent .entry-meta { margin-right: -10px; }

.ievent .entry-meta li {
	font-size: 13px;
	margin-right: 10px;
	padding-left: 10px;
}

.ievent .entry-meta li:first-child { padding-left: 0; }


