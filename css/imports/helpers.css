
/*-----------------------------------------------------------------------------------

    helpers.css

-----------------------------------------------------------------------------------*/
.uppercase { text-transform: uppercase !important; }

.lowercase { text-transform: lowercase !important; }

.capitalize { text-transform: capitalize !important; }

.nott { text-transform: none !important; }

.tright { text-align: right !important; }

.tleft { text-align: left !important; }

.fright { float: right !important; }

.fleft { float: left !important; }

.fnone { float: none !important; }

.ohidden {
	position: relative;
	overflow: hidden !important;
}


.line,
.double-line {
	clear: both;
	position: relative;
	width: 100%;
	margin: 60px 0;
	border-top: 1px solid #EEE;
}

.line.line-sm { margin: 30px 0; }

span.middot {
	display: inline-block;
	margin: 0 5px;
}

.double-line { border-top: 3px double #E5E5E5; }

.emptydiv {
	display: block !important;
	position: relative !important;
}

.allmargin { margin: 50px !important; }

.leftmargin { margin-left: 50px !important; }

.rightmargin { margin-right: 50px !important; }

.topmargin { margin-top: 50px !important; }

.bottommargin { margin-bottom: 50px !important; }

.clear-bottommargin { margin-bottom: -50px !important; }

.allmargin-sm { margin: 30px !important; }

.leftmargin-sm { margin-left: 30px !important; }

.rightmargin-sm { margin-right: 30px !important; }

.topmargin-sm { margin-top: 30px !important; }

.bottommargin-sm { margin-bottom: 30px !important; }

.clear-bottommargin-sm { margin-bottom: -30px !important; }

.allmargin-lg { margin: 80px !important; }

.leftmargin-lg { margin-left: 80px !important; }

.rightmargin-lg { margin-right: 80px !important; }

.topmargin-lg { margin-top: 80px !important; }

.bottommargin-lg { margin-bottom: 80px !important; }

.clear-bottommargin-lg { margin-bottom: -80px !important; }

.nomargin {margin: 0 !important; }

.noleftmargin { margin-left: 0 !important; }

.norightmargin { margin-right: 0 !important; }

.notopmargin { margin-top: 0 !important; }

.nobottommargin { margin-bottom: 0 !important; }

.header-stick { margin-top: -50px !important; }

.content-wrap .header-stick { margin-top: -80px !important; }

.footer-stick { margin-bottom: -50px !important; }

.content-wrap .footer-stick { margin-bottom: -80px !important; }

.noborder { border: none !important; }

.noleftborder { border-left: none !important; }

.norightborder { border-right: none !important; }

.notopborder { border-top: none !important; }

.nobottomborder { border-bottom: none !important; }

.noradius { -webkit-border-radius: 0 !important; -moz-border-radius: 0 !important; -ms-border-radius: 0 !important; -o-border-radius: 0 !important; border-radius: 0 !important; }

.col-padding { padding: 60px; }

.nopadding { padding: 0 !important; }

.noleftpadding { padding-left: 0 !important; }

.norightpadding { padding-right: 0 !important; }

.notoppadding { padding-top: 0 !important; }

.nobottompadding { padding-bottom: 0 !important; }

.noabsolute { position: relative !important; }

.noshadow { box-shadow: none !important; }

.notextshadow { text-shadow: none !important; }

.hidden { display: none !important; }

.nothidden { display: block !important; }

.inline-block {
	float: none !important;
	display: inline-block !important;
}

.center { text-align: center !important; }

.divcenter {
	position: relative !important;
	float: none !important;
	margin-left: auto !important;
	margin-right: auto !important;
}

.bgcolor,
.bgcolor #header-wrap { background-color: #1ABC9C !important; }

.color { color: #1ABC9C !important; }

.border-color { border-color: #1ABC9C !important; }

.nobg { background: none !important; }

.nobgcolor { background-color: transparent !important; }

.t300 { font-weight: 300 !important; }

.t400 { font-weight: 400 !important; }

.t500 { font-weight: 500 !important; }

.t600 { font-weight: 600 !important; }

.t700 { font-weight: 700 !important; }

.ls0 { letter-spacing: 0px !important; }

.ls1 { letter-spacing: 1px !important; }

.ls2 { letter-spacing: 2px !important; }

.ls3 { letter-spacing: 3px !important; }

.ls4 { letter-spacing: 4px !important; }

.ls5 { letter-spacing: 5px !important; }

.noheight { height: 0 !important; }

.nolineheight { line-height: 0 !important; }

.font-body { font-family: 'Lato', sans-serif; }

.font-primary { font-family: 'Raleway', sans-serif; }

.font-secondary { font-family: 'Crete Round', serif; }

.bgicon {
	display: block;
	position: absolute;
	bottom: -60px;
	right: -50px;
	font-size: 210px;
	color: rgba(0,0,0,0.1);
}

.imagescale {
	display: block;
	overflow: hidden;
}

.imagescale img {
	-webkit-transform: scale(1.1);
	transform: scale(1.1);
	-webkit-transition: all 1s ease;
	transition: all 1s ease;
}

.imagescale:hover img {
	-webkit-transform: scale(1);
	transform: scale(1);
}

.grayscale {
	filter: brightness(80%) grayscale(1) contrast(90%);
	-webkit-filter: brightness(80%) grayscale(1) contrast(90%);
	-moz-filter: brightness(80%) grayscale(1) contrast(90%);
	-o-filter: brightness(80%) grayscale(1) contrast(90%);
	-ms-filter: brightness(80%) grayscale(1) contrast(90%);
	transition: 1s filter ease;
	-webkit-transition: 1s -webkit-filter ease;
	-moz-transition: 1s -moz-filter ease;
	-ms-transition: 1s -ms-filter ease;
	-o-transition: 1s -o-filter ease;
}

.grayscale:hover {
	filter: brightness(100%) grayscale(0);
	-webkit-filter: brightness(100%) grayscale(0);
	-moz-filter: brightness(100%) grayscale(0);
	-o-filter: brightness(100%) grayscale(0);
	-ms-filter: brightness(100%) grayscale(0);
}

/* MOVING BG -  TESTIMONIALS */
.bganimate {
	-webkit-animation:BgAnimated 30s infinite linear;
	 -moz-animation:BgAnimated 30s infinite linear;
		-ms-animation:BgAnimated 30s infinite linear;
		 -o-animation:BgAnimated 30s infinite linear;
			animation:BgAnimated 30s infinite linear;
}
 @-webkit-keyframes BgAnimated {
	from  {background-position:0 0;}
	to    {background-position:0 400px;}
	}
 @-moz-keyframes BgAnimated {
	from  {background-position:0 0;}
	to    {background-position:0 400px;}
	}
 @-ms-keyframes BgAnimated {
	from  {background-position:0 0;}
	to    {background-position:0 400px;}
	}
 @-o-keyframes BgAnimated {
	from  {background-position:0 0;}
	to    {background-position:0 400px;}
	}
 @keyframes BgAnimated {
	from  {background-position:0 0;}
	to    {background-position:0 400px;}
	}


.input-block-level {
	display: block;
	width: 100% !important;
	min-height: 30px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}

.vertical-middle {
	height: auto !important;
	left: 0;
	max-width: none !important;
	z-index: 2;
}

.vertical-middle + .video-wrap { z-index: 1 !important; }

.magnific-max-width .mfp-content { max-width: 800px; }
