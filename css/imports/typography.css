/* ----------------------------------------------------------------
	1. Basic
-----------------------------------------------------------------*/

dl, dt, dd, ol, ul, li {
	margin: 0;
	padding: 0;
}

.clear {
	clear: both;
	display: block;
	font-size: 0px;
	height: 0px;
	line-height: 0;
	width: 100%;
	overflow:hidden;
}

::selection {
	background: #1ABC9C;
	color: #FFF;
	text-shadow: none;
}

::-moz-selection {
	background: #1ABC9C; /* Firefox */
	color: #FFF;
	text-shadow: none;
}

::-webkit-selection {
	background: #1ABC9C; /* Safari */
	color: #FFF;
	text-shadow: none;
}

:active,
:focus { outline: none !important; }


/* ----------------------------------------------------------------
	2. Typography
-----------------------------------------------------------------*/


body {
	line-height: 1.5;
	color: #555;
	font-family: 'Lato', sans-serif;
}

a {
	text-decoration: none !important;
	color: #1ABC9C;
}

a:hover { color: #222; }

a img { border: none; }

img { max-width: 100%; }

iframe { border: none !important; }


/* ----------------------------------------------------------------
	3. Basic Layout Styles
-----------------------------------------------------------------*/


h1,
h2,
h3,
h4,
h5,
h6 {
	color: #444;
	font-weight: 600;
	line-height: 1.5;
	margin: 0 0 30px 0;
	font-family: 'Raleway', sans-serif;
}

h5,
h6 { margin-bottom: 20px; }

h1 { font-size: 36px; }

h2 { font-size: 30px; }

h3 { font-size: 24px; }

h4 { font-size: 18px; }

h5 { font-size: 14px; }

h6 { font-size: 12px; }

h4 { font-weight: 600; }

h5,
h6 { font-weight: bold; }

h1 > span:not(.nocolor):not(.badge),
h2 > span:not(.nocolor):not(.badge),
h3 > span:not(.nocolor):not(.badge),
h4 > span:not(.nocolor):not(.badge),
h5 > span:not(.nocolor):not(.badge),
h6 > span:not(.nocolor):not(.badge) { color: #1ABC9C; }

p,
pre,
ul,
ol,
dl,
dd,
blockquote,
address,
table,
fieldset,
form { margin-bottom: 30px; }

small { font-family: 'Lato', sans-serif; }


#wrapper {
	position: relative;
	float: none;
	width: 1220px;
	margin: 0 auto;
	background-color: #FFF;
	box-shadow: 0 0 10px rgba(0,0,0,0.1);
	-moz-box-shadow: 0 0 10px rgba(0,0,0,0.1);
	-webkit-box-shadow: 0 0 10px rgba(0,0,0,0.1);
}