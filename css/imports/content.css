
/*-----------------------------------------------------------------------------------

    content.css

-----------------------------------------------------------------------------------*/



/* ----------------------------------------------------------------
	Content
-----------------------------------------------------------------*/


#content {
	position: relative;
	overflow: hidden;
	background-color: #FFF;
}

#content p { line-height: 1.8; }

.content-wrap {
	position: relative;
	padding: 80px 0;
}

#content .container { position: relative; }

.grid-container {
	position: relative;
	overflow: hidden;
}

body:not(.device-touch) .grid-container {
	-webkit-transition: height .4s ease;
	-o-transition: height .4s ease;
	transition: height .4s ease;
}