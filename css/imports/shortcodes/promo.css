
/*-----------------------------------------------------------------------------------

    Shortcodes: promo.css

-----------------------------------------------------------------------------------*/



/* ----------------------------------------------------------------
	Promo Boxes
-----------------------------------------------------------------*/


.promo {
	position: relative;
	padding: 30px 200px 30px 0;
}

.promo h3 {
	font-weight: bold;
	margin-bottom: 0;
}

.promo > span,
.promo > .container > span {
	display: block;
	color: #444;
	font-weight: 300;
	font-size: 16px;
	margin-top: 6px;
}

.promo a.button {
	position: absolute;
	top: 50%;
	margin: 0;
	margin-top: -18px;
	left: auto;
	right: 0;
}

.promo a.button.button-mini { margin-top: -12px; }

.promo a.button.button-small { margin-top: -15px; }

.promo a.button.button-large { margin-top: -22px; }

.promo a.button.button-xlarge { margin-top: -25px; }

.promo a.button.button-desc { margin-top: -40px; }


/* Promo Uppercase
-----------------------------------------------------------------*/

.promo-uppercase { text-transform: uppercase; }

.promo-uppercase h3 { font-size: 22px; }

.promo-uppercase > span,
.promo-uppercase > .container > span { font-size: 15px; }


/* Promo - with Border
-----------------------------------------------------------------*/

.promo.promo-border {
	border: 1px solid #E5E5E5;
	border-radius: 3px;
	padding-left: 30px;
}

.promo.promo-border a.button { right: 30px; }


/* Promo - Light
-----------------------------------------------------------------*/

.promo.promo-light {
	background-color: #F5F5F5;
	border-radius: 3px;
	padding-left: 30px;
}

.promo.promo-light a.button { right: 30px; }


/* Promo - Dark
-----------------------------------------------------------------*/

.promo.promo-dark {
	border: 0 !important;
	background-color: #333;
	padding: 30px 200px 30px 30px;
	text-shadow: 1px 1px 1px rgba(0,0,0,0.2);
	border-radius: 3px;
}

.promo.promo-dark h3 { color: #FFF; }

.promo.promo-dark > h3 span,
.promo.promo-dark > .container > h3 span {
	padding: 2px 8px;
	border-radius: 3px;
	background-color: rgba(0,0,0,0.15);
	color: #FFF;
	font-weight: 400;
}

.promo.promo-dark > span,
.promo.promo-dark > .container > span {
	color: #CCC;
	margin-top: 8px;
}

.promo.promo-dark a.button { right: 30px; }


/* Promo - Flat
-----------------------------------------------------------------*/

.promo.promo-flat { background-color: #1ABC9C; }

.promo.promo-flat > span,
.promo.promo-flat > .container > span { color: rgba(255,255,255,0.9); }

.promo.promo-flat a.button:not(.button-border) {
	background-color: rgba(0,0,0,0.2);
	color: #FFF;
}

.promo.promo-flat a.button:hover {
	background-color: #FFF;
	border-color: #FFF;
	color: #444;
	text-shadow: none;
}


/* Promo - 100% Full Width
-----------------------------------------------------------------*/

.promo-full {
	border-radius: 0 !important;
	border-left: 0 !important;
	border-right: 0 !important;
	padding: 40px 0 !important;
}

.promo-full a.button { right: 15px !important; }

.promo-full.promo-right a.button {
	right: auto !important;
	left: 15px !important;
}

/* Promo - Parallax
-----------------------------------------------------------------*/

.promo.parallax { padding: 80px 0 !important; }


/* Promo - Right Align
-----------------------------------------------------------------*/

.promo.promo-right {
	text-align: right;
	padding: 30px 0 30px 200px;
}

.promo.promo-right a.button {
	left: 0;
	right: auto;
}


/* Promo - with Border & Right Align
-----------------------------------------------------------------*/

.promo.promo-right.promo-border:not(.promo-mini),
.promo.promo-dark.promo-right:not(.promo-mini) { padding-right: 30px; }

.promo.promo-right.promo-border a.button,
.promo.promo-dark.promo-right a.button { left: 30px; }


/* Promo - Center Align
-----------------------------------------------------------------*/

.promo.promo-center {
	text-align: center;
	padding: 30px 20px;
}

.promo.promo-center a.button {
	position: relative;
	top: 0;
	margin: 20px 0 0;
	left: 0 !important;
	right: 0;
}


/* Promo - Mini
-----------------------------------------------------------------*/

.promo.promo-mini {
	text-align: left;
	padding: 20px 25px;
}

.promo.promo-mini h3 {
	font-size: 20px;
	line-height: 1.5;
}

.promo.promo-mini.promo-uppercase h3 { font-size: 18px; }

.promo.promo-mini.promo-dark h3 { line-height: 1.7; }

.promo.promo-mini a.button {
	position: relative;
	top: 0;
	margin: 20px 0 0;
	left: 0 !important;
	right: 0;
}


/* Promo - Mini - Center Align
-----------------------------------------------------------------*/

.promo.promo-mini.promo-center { text-align: center; }


/* Promo - Mini - Right Align
-----------------------------------------------------------------*/

.promo.promo-mini.promo-right { text-align: right; }