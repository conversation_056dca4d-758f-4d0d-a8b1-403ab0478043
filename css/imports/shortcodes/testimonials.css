
/*-----------------------------------------------------------------------------------

    Shortcodes: testimonials.css

-----------------------------------------------------------------------------------*/



/* Testimonials - Grid
-----------------------------------------------------------------*/

.testimonials-grid li {
	width: 50%;
	padding: 25px;
}

.testimonials-grid.grid-1 li { width: 100%; }

.testimonials-grid.grid-3 li { width: 33.33%; }

.testimonials-grid li .testimonial {
	padding: 0;
	background-color: transparent !important;
	border: 0 !important;
	box-shadow: none !important;
}


/* Testimonials - Item
-----------------------------------------------------------------*/

.testimonial {
	position: relative;
	padding: 20px;
	background-color: #FFF;
	border: 1px solid rgba(0,0,0,0.1);
	border-radius: 5px;
	box-shadow: 0 1px 1px rgba(0,0,0,0.1);
}

.testi-image {
	float: left;
	margin-right: 15px;
}

.testi-image,
.testi-image a,
.testi-image img,
.testi-image i {
	display: block;
	width: 64px;
	height: 64px;
}

.testi-image i {
	text-align: center;
	background-color: #EEE;
	border-radius: 50%;
	line-height: 64px;
	font-size: 28px;
	color: #888;
	text-shadow: 1px 1px 1px #FFF;
}

.testi-image img { border-radius: 50%; }

.testi-content {
	position: relative;
	overflow: hidden;
}

.testi-content p {
	margin-bottom: 0;
	font-family: 'Crete Round', serif;
	font-style: italic;
}

.testi-content p:before,
.testi-content p:after { content: '"'; }

.testi-meta {
	margin-top: 10px;
	font-size: 13px;
	font-weight: bold;
	text-transform: uppercase;
}

.testi-meta:before { content: '\2013'; }

.testi-meta span {
	display: block;
	font-weight: normal;
	color: #999;
	font-size: 12px;
	text-transform: none;
	padding-left: 10px;
}


/* Twitter - Small Scroller
-----------------------------------------------------------------*/

.testimonial.twitter-scroll .testi-content p:before,
.testimonial.twitter-scroll .testi-content p:after,
.testimonial.twitter-scroll .testi-meta:before { content: ''; }

.testimonial.twitter-scroll .testi-meta span { padding-left: 0; }

.testimonial.twitter-scroll .testi-meta span a { color: #999; }

.testimonial.twitter-scroll .testi-meta span a:hover { color: #222; }


/* Testimonials - Full Width
-----------------------------------------------------------------*/

.testimonial.testimonial-full { padding: 30px; }

.testimonial.testimonial-full[data-animation="fade"] { padding: 30px; }

.testimonial.testimonial-full[data-animation="fade"] .flexslider { overflow: visible !important; }

.testimonial-full .testi-image {
	float: none;
	margin: 0 auto 20px;
}

.testimonial-full .testi-image,
.testimonial-full .testi-image a,
.testimonial-full .testi-image img,
.testimonial-full .testi-image i {
	display: block;
	width: 72px;
	height: 72px;
}

.testimonial-full .testi-image i { line-height: 72px; }

.testimonial-full .testi-content {
	text-align: center;
	font-size: 18px;
}

.testimonial-full .testi-meta { margin-top: 15px; }

.testimonial-full .testi-meta span { padding-left: 0; }


/* Testimonial - Section Scroller
-----------------------------------------------------------------*/

.section > .testimonial {
	padding: 0;
	background-color: transparent !important;
	border: 0 !important;
	box-shadow: none !important;
	max-width: 800px;
	margin: 0 auto;
}

.section > .testimonial[data-animation="fade"] { padding: 0; }

.section > .testimonial .testi-content { font-size: 22px; }

.section > .testimonial .testi-meta,
.section > .testimonial .testi-meta span { font-size: 14px; }


/* Testimonial - Pagination
-----------------------------------------------------------------*/

.testimonial .flex-control-nav {
	top: auto;
	bottom: 6px;
	right: 0;
}

.testimonial .flex-control-nav li {
	margin: 0 2px;
	width: 6px;
	height: 6px;
}

.testimonial .flex-control-nav li a {
	width: 6px !important;
	height: 6px !important;
	border: none;
	background-color: #1ABC9C;
	opacity: 0.5;
}

.testimonial .flex-control-nav li:hover a { opacity: 0.75; }
.testimonial .flex-control-nav li a.flex-active { opacity: 1; }


/* Testimonial - Full Scroller
-----------------------------------------------------------------*/

.testimonial.testimonial-full .flex-control-nav {
	position: relative;
	top: 0;
	bottom: 0;
	right: 0;
	left: 0;
	height: 6px;
	margin-top: 20px;
}

.testimonial.testimonial-full .flex-control-nav li {
	display: inline-block;
	float: none;
}