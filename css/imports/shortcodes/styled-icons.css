
/*-----------------------------------------------------------------------------------

    Shortcodes: styled-icons.css

-----------------------------------------------------------------------------------*/


/* ----------------------------------------------------------------
	Styled Icons
-----------------------------------------------------------------*/


.i-rounded,
.i-plain,
.i-circled,
.i-bordered,
.social-icon {
	display: block;
	float: left;
	margin: 4px 11px 7px 0;
	text-align: center !important;
	font-size: 28px;
	color: #FFF;
	text-shadow: 1px 1px 1px rgba(0,0,0,0.3);
	cursor: pointer;
	font-style: normal;
}

body:not(.device-touch) .i-rounded,
body:not(.device-touch) .i-plain,
body:not(.device-touch) .i-circled,
body:not(.device-touch) .i-bordered,
body:not(.device-touch) .social-icon {
	-webkit-transition: all .3s ease;
	-o-transition: all .3s ease;
	transition: all .3s ease;
}

.i-rounded,
.i-circled,
.i-bordered {
	width: 52px !important;
	height: 52px !important;
	line-height: 52px !important;
	background-color: #444;
	border-radius: 3px;
}

.i-bordered {
	border: 1px solid #555;
	line-height: 50px !important;
	background-color: transparent !important;
	color: #444;
	text-shadow: none;
}

.i-plain {
	width: 36px !important;
	height: 36px !important;
	font-size: 28px;
	line-height: 36px !important;
	color: #444;
	text-shadow: 1px 1px 1px #FFF;
}

.i-plain:hover { color: #777; }

.i-circled { border-radius: 50%; }

.i-light {
	background-color: #F5F5F5;
	color: #444;
	text-shadow: 1px 1px 1px #FFF;
	box-shadow: inset 0 0 15px rgba(0,0,0,0.2);
}

.i-alt {
	background-image: url('../../../images/icons/iconalt.svg');
	background-position: center center;
	background-size: 100% 100%;
}


.i-rounded:hover,
.i-circled:hover {
	background-color: #1ABC9C;
	color: #FFF;
	text-shadow: 1px 1px 1px rgba(0,0,0,0.3);
	box-shadow: 0 0 0 rgba(0,0,0,0.2);
}

.i-bordered:hover {
	background-color: #444 !important;
	color: #FFF;
	border-color: #444;
}


/* Icon Size - Small
-----------------------------------------------------------------*/

.i-small.i-rounded,
.i-small.i-plain,
.i-small.i-circled,
.i-small.i-bordered {
	margin: 4px 11px 7px 0;
	font-size: 14px;
}

.i-small.i-rounded,
.i-small.i-circled,
.i-small.i-bordered {
	width: 28px !important;
	height: 28px !important;
	line-height: 28px !important;
}

.i-small.i-bordered { line-height: 26px !important; }

.i-small.i-plain {
	width: 18px !important;
	height: 18px !important;
	font-size: 14px;
	line-height: 18px !important;
}


/* Icon Size - Small
-----------------------------------------------------------------*/

.i-medium.i-rounded,
.i-medium.i-plain,
.i-medium.i-circled,
.i-medium.i-bordered {
	margin: 6px 13px 9px 0;
	font-size: 21px;
}

.i-medium.i-rounded,
.i-medium.i-circled,
.i-medium.i-bordered {
	width: 36px !important;
	height: 36px !important;
	line-height: 36px !important;
}

.i-medium.i-bordered { line-height: 34px !important; }

.i-medium.i-plain {
	width: 28px !important;
	height: 28px !important;
	font-size: 20px;
	line-height: 28px !important;
}


/* Icon Size - Large
-----------------------------------------------------------------*/

.i-large.i-rounded,
.i-large.i-plain,
.i-large.i-circled,
.i-large.i-bordered {
	margin: 4px 11px 7px 0;
	font-size: 42px;
}

.i-large.i-rounded,
.i-large.i-circled,
.i-large.i-bordered {
	width: 64px !important;
	height: 64px !important;
	line-height: 64px !important;
}

.i-large.i-bordered { line-height: 62px !important; }

.i-large.i-plain {
	width: 48px !important;
	height: 48px !important;
	font-size: 42px;
	line-height: 48px !important;
}


/* Icon Size - Extra Large
-----------------------------------------------------------------*/

.i-xlarge.i-rounded,
.i-xlarge.i-plain,
.i-xlarge.i-circled,
.i-xlarge.i-bordered {
	margin: 4px 11px 7px 0;
	font-size: 56px;
}

.i-xlarge.i-rounded,
.i-xlarge.i-circled,
.i-xlarge.i-bordered {
	width: 84px !important;
	height: 84px !important;
	line-height: 84px !important;
}

.i-xlarge.i-bordered { line-height: 82px !important; }

.i-xlarge.i-plain {
	width: 64px !important;
	height: 64px !important;
	font-size: 56px;
	line-height: 64px !important;
}


/* ----------------------------------------------------------------
	Icon Overlay
-----------------------------------------------------------------*/

.i-overlay,
.i-overlay a {
	display: block;
	position: absolute;
	width: 100% !important;
	height: 100% !important;
	top: 0;
	left: 0;
	text-align: center;
	background: rgba(0,0,0,0.2) url('images/grid.png') repeat;
}

.i-overlay a { background: transparent; }

.i-overlay img,
.i-overlay i {
	display: block;
	position: absolute;
	top: 50%;
	left: 50%;
	margin: -24px 0 0 -24px;
	width: 48px !important;
	height: 48px !important;
	font-size: 48px;
	line-height: 1;
	color: #E5E5E5;
	text-shadow: 1px 1px 1px rgba(0,0,0,0.15);
}

.i-overlay i {
	margin: -18px 0 0 -18px;
	width: 36px;
	height: 36px;
	line-height: 36px;
	font-size: 24px;
}

.on-hover.i-overlay { opacity: 0; }

body:not(.device-touch) .on-hover.i-overlay {
	-webkit-transition: opacity 0.3s linear;
	-o-transition: opacity 0.3s linear;
	transition: opacity 0.3s linear;
}

.on-hover.i-overlay:hover { opacity: 1; }