$main-font:'Roboto', sans-serif;
$condensed-font:'Roboto Condensed', sans-serif;
$header-font:'bebas_neueregular', sans-serif;
$maroon:#b62554;
$blue:#1c3666;
$lt-blue:#76a1d4;
$dk-blue:#090e33;

body {
	font-size:1rem;
	&.dark {
		background-color:$dk-blue !important;
	}
}
p {
	margin-bottom:15px !important;
}
#primary-menu {
	ul {
		li {
			a {
				font-size:14px !important;
				letter-spacing:3px !important;
				font-family:'Roboto Condensed', sans-serif !important;
				&:hover {
					color:#e0b5b8 !important;
				}
			}
		}
	}
}
.testi-content {
	p {
		font-family:$header-font !important;
		text-transform:uppercase;
		letter-spacing:2px;
		font-style:normal;
		font-size:23px;
		color:$maroon;
		line-height:1.5 !important;
	}
}
.testi-meta {
	font-family:$condensed-font !important;
	font-weight:normal;
	letter-spacing:3px;
	span {
		font-family:$main-font !important;
		letter-spacing:1px;
		margin-top:5px;
		color:#879298;
	}
}

.play-icon {
	position: relative;
	display: block;
	font-size: 28px;
	width: 60px;
	height: 60px;
	line-height: 62px;
	left: 50%;
	margin-left: -30px;
	border-radius: 50%;
	color: $maroon !important;
	background-color: #FFF;
	-webkit-transition: transform .3s ease;
	-o-transition: transform .3s ease;
	transition: transform .3s ease;
	i {
		position: relative;
		left: 4px;
	}
	&:hover {
		-webkit-transform: scale(1.15);
		-ms-transform: scale(1.15);
		-o-transform: scale(1.15);
		transform: scale(1.15);
	}
}
.heading-block {
	h2 {
		font-size:20px !important;
	}
}

.modal1 {
	p {
		font-size:12px !important;
	}
}
.event-date {    
	background: url('../images/event-date.svg') no-repeat;
	color: #16216a;
	float: left;
	font-size: 28px;
	font-weight: 400;
	height: 65px;
	line-height: 38px;
	margin: 0 23px 0 0px;
	padding: 3px 0;
	position: relative;
	text-align: center;
	width: 50px;
	text-shadow: 0 1px 0 #fff;
	display: block;
	span {
		color: #fff !important;
		text-shadow: 0 -1px 0 rgba(0,0,0,0.2);
		text-transform: uppercase !important;
		margin-top: 3px;
		    display: block;
    font-size: 15px;
    letter-spacing: 1px;
    line-height: 18px;
	}
}
.toggle {
	overflow:auto;
	img {
		border-radius: 2px;
	}
	.togglet {
		overflow:auto;
		i {
			right:24px !important;
			font-size:22px;
		}
	}
}
.togglet {
	h3{
		display: block;
		position: relative;
		padding: 10px 10px 0 0;
		margin: 0;
		font-size: 23px;
		font-weight: 400;
		color: #444;
		cursor: pointer;
		letter-spacing: 2px;
		text-transform: uppercase;
		font-family:$condensed-font;
		line-height: 1.2;
		span{
			text-transform: none;
			display: block;
			font-size: 15px;
			letter-spacing: 1px;
			line-height: 18px;
			padding-bottom: 10px;
			color: #a1afd6 !important;
			font-weight:normal;
			font-family:$main-font !important;
		}

	}
}


h2 {
	font-size:50px;
}
.section-title{
	font-family: $condensed-font !important;
	font-weight: 400;
	text-transform: uppercase;
}
.ls7 {
	letter-spacing:7px !important;
}
.iconlist {
	li {
		position: relative;
		border-bottom: 1px dotted #bbb;
		padding: 8px 0 8px 30px;
		&:last-child {
			margin-bottom:15px;
		}
	}
}
.iconlist > li [class^="icon-"]:first-child, .iconlist > li [class*=" icon-"]:first-child {
	left: 0 !important;
	top: 8px !important;
}
.iconlist {
	margin-left:0 !important;
}
.dark {
	.toggle {
		&.toggle-bg {
			.togglet {
				border-radius: 2px !important;
				background: rgba(24,46,92,1);
				background: -moz-linear-gradient(top, rgba(24,46,92,1) 0%, rgba(22,40,76,1) 100%);
				background: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(24,46,92,1)), color-stop(100%, rgba(22,40,76,1)));
				background: -webkit-linear-gradient(top, rgba(24,46,92,1) 0%, rgba(22,40,76,1) 100%);
				background: -o-linear-gradient(top, rgba(24,46,92,1) 0%, rgba(22,40,76,1) 100%);
				background: -ms-linear-gradient(top, rgba(24,46,92,1) 0%, rgba(22,40,76,1) 100%);
				background: linear-gradient(to bottom, rgba(24,46,92,1) 0%, rgba(22,40,76,1) 100%);
				filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#182e5c', endColorstr='#16284c', GradientType=0 );
				border:1px solid #173554;
				padding:5px 30px !important;
			}
		}
	}
	.testimonial {
		background-color:$dk-blue !important;
		border:2px solid $maroon !important;
	}
	.counter {
		&.counter-lined {
			+ h5 {
				&:before {
					border-color:#516282 !important;
				}
			} 
		}
	}
	.section {
		background-color:$dk-blue !important;
	}
	.feature-box {
		h3 {
			color:#eee !important;
			margin-top:40px !important;
		}
		p {
			color:#fff !important;
		}
	}
	.si-dark {
		background-color:$maroon !important;
		color: #080b34 !important;
		}
	.social-icon {
			i {
				&:last-child {
					color: #080b34 !important;
			}
		}
		}
	#copyrights {
		color:rgba(255,255,255,0.4);
	}
}
.button-pink {
	background: rgba(182,37,84,1);
	background: -moz-linear-gradient(top, rgba(182,37,84,1) 21%, rgba(182,37,84,1) 22%, rgba(161,27,72,1) 78%);
	background: -webkit-gradient(left top, left bottom, color-stop(21%, rgba(182,37,84,1)), color-stop(22%, rgba(182,37,84,1)), color-stop(78%, rgba(161,27,72,1)));
	background: -webkit-linear-gradient(top, rgba(182,37,84,1) 21%, rgba(182,37,84,1) 22%, rgba(161,27,72,1) 78%);
	background: -o-linear-gradient(top, rgba(182,37,84,1) 21%, rgba(182,37,84,1) 22%, rgba(161,27,72,1) 78%);
	background: -ms-linear-gradient(top, rgba(182,37,84,1) 21%, rgba(182,37,84,1) 22%, rgba(161,27,72,1) 78%);
	background: linear-gradient(to bottom, rgba(182,37,84,1) 21%, rgba(182,37,84,1) 22%, rgba(161,27,72,1) 78%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#b62554', endColorstr='#a11b48', GradientType=0 );
	color:#fff;
	-webkit-transition: all .2s ease-in-out;
	-o-transition: all .2s ease-in-out;
	transition: all .2s ease-in-out;
	&:hover {
		background: rgba(224,52,109,1);
		background: -moz-linear-gradient(top, rgba(224,52,109,1) 22%, rgba(183,37,84,1) 78%, rgba(182,37,84,1) 79%);
		background: -webkit-gradient(left top, left bottom, color-stop(22%, rgba(224,52,109,1)), color-stop(78%, rgba(183,37,84,1)), color-stop(79%, rgba(182,37,84,1)));
		background: -webkit-linear-gradient(top, rgba(224,52,109,1) 22%, rgba(183,37,84,1) 78%, rgba(182,37,84,1) 79%);
		background: -o-linear-gradient(top, rgba(224,52,109,1) 22%, rgba(183,37,84,1) 78%, rgba(182,37,84,1) 79%);
		background: -ms-linear-gradient(top, rgba(224,52,109,1) 22%, rgba(183,37,84,1) 78%, rgba(182,37,84,1) 79%);
		background: linear-gradient(to bottom, rgba(224,52,109,1) 22%, rgba(183,37,84,1) 78%, rgba(182,37,84,1) 79%);
		filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#e0346d', endColorstr='#b62554', GradientType=0 );
	}
}
.bottommargin-xsm {
	margin-bottom: 15px !important;
}
#section-subscribe, #section-sponsors {
	background: rgba(9,14,51,1);
	background: -moz-linear-gradient(top, rgba(9,14,51,1) 0%, rgba(15,44,82,1) 100%);
	background: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(9,14,51,1)), color-stop(100%, rgba(15,44,82,1)));
	background: -webkit-linear-gradient(top, rgba(9,14,51,1) 0%, rgba(15,44,82,1) 100%);
	background: -o-linear-gradient(top, rgba(9,14,51,1) 0%, rgba(15,44,82,1) 100%);
	background: -ms-linear-gradient(top, rgba(9,14,51,1) 0%, rgba(15,44,82,1) 100%);
	background: linear-gradient(to bottom, rgba(9,14,51,1) 0%, rgba(15,44,82,1) 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#090e33', endColorstr='#0f2c52', GradientType=0 );
}
.heading-block {
	h3 {
		font-weight:400;
	}
}
.lt-blue {
	color: $lt-blue !important;
}
.feature-box {
	&.fbox-center {
		&.fbox-plain {
			.fbox-icon {
				i {
					font-size:55px !important;
				}
			}
		}
	}
}
.input-group-lg > .form-control, .input-group-lg > .input-group-addon, .input-group-lg > .input-group-btn > .btn {
	height: 46px;
	padding: 10px 16px;
	font-size: 18px;
	line-height: 1.3333333;
	border-radius: 6px;
}
.input-group-addon {
	background-color: #2b3d6b;
	color: #6ca2d4;
	border: 1px solid #234977;
}
.input-group .form-control:first-child, .input-group-addon:first-child, .input-group-btn:first-child > .btn, .input-group-btn:first-child > .btn-group > .btn, .input-group-btn:first-child > .dropdown-toggle, .input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle), .input-group-btn:last-child > .btn-group:not(:last-child) > .btn {
	border-bottom-right-radius: 0;
	border-top-right-radius: 0;
}
.input-group .form-control:last-child, .input-group-addon:last-child, .input-group-btn:last-child > .btn, .input-group-btn:last-child > .btn-group > .btn, .input-group-btn:last-child > .dropdown-toggle, .input-group-btn:first-child > .btn:not(:first-child), .input-group-btn:first-child > .btn-group:not(:first-child) > .btn {
	border-bottom-left-radius: 0;
	border-top-left-radius: 0;
}
.input-group-btn:last-child > .btn, .input-group-btn:last-child > .btn-group {
	z-index: 2;
	margin-left: -1px;
}

/* Hamburger menu */

#nav-icon4 {
	width: 25px;
	height: 20px;
	position: relative;
	// margin: 50px auto;
	-webkit-transform: rotate(0deg);
	-moz-transform: rotate(0deg);
	-o-transform: rotate(0deg);
	transform: rotate(0deg);
	-webkit-transition: .5s ease-in-out;
	-moz-transition: .5s ease-in-out;
	-o-transition: .5s ease-in-out;
	transition: .5s ease-in-out;
	cursor: pointer;
	float:left;
	span {
		display: block;
		position: absolute;
		height: 2px;
		width: 100%;
		background: #fff;
		border-radius: 9px;
		opacity: 1;
		left: 0;
		-webkit-transform: rotate(0deg);
		-moz-transform: rotate(0deg);
		-o-transform: rotate(0deg);
		transform: rotate(0deg);
		-webkit-transition: .25s ease-in-out;
		-moz-transition: .25s ease-in-out;
		-o-transition: .25s ease-in-out;
		transition: .25s ease-in-out;
		&:nth-child(1) {
			top: 0px;
			-webkit-transform-origin: left center;
			-moz-transform-origin: left center;
			-o-transform-origin: left center;
			transform-origin: left center;
		}
		&:nth-child(2){
			top: 7px;
			-webkit-transform-origin: left center;
			-moz-transform-origin: left center;
			-o-transform-origin: left center;
			transform-origin: left center;
		}
		&:nth-child(3) {
			top: 14px;
			-webkit-transform-origin: left center;
			-moz-transform-origin: left center;
			-o-transform-origin: left center;
			transform-origin: left center;
		}
	}
	&.open {
		span {
			&:nth-child(1) {
				-webkit-transform: rotate(45deg);
				-moz-transform: rotate(45deg);
				-o-transform: rotate(45deg);
				transform: rotate(45deg);
				top: -4px;
				left: 6px;
			}
			&:nth-child(2) {
				width: 0%;
				opacity: 0;
			}
			&:nth-child(3) {
				-webkit-transform: rotate(-45deg);
				-moz-transform: rotate(-45deg);
				-o-transform: rotate(-45deg);
				transform: rotate(-45deg);
				top: 14px;
				left: 6px;
			}
		}
	}
}
#primary-menu-trigger, #page-submenu-trigger {
	top:43px !important;
	width:25px !important;
	height:20px !important;
	left:40px !important;
}
.testi-image {
	float:none;
	margin: auto;
	margin-bottom: 20px;
}
.testimonial {
	&:before {
		content: "";
		position: absolute;
		bottom: -20px;
		left: 40px;
		border-width: 18px 18px 0;
		border-style: solid;
		border-color: $maroon transparent;
		display: block;
		width: 0;
	}
	&:after {
		content: "";
		position: absolute;
		bottom: -17px;
		left: 41px;
		border-width: 17px 17px 0;
		border-style: solid;
		border-color: $dk-blue transparent;
		display: block;
		width: 0;
	}
}
.counter {
	font-weight: 400;
	color: $maroon;
}
.page-section{
	border-bottom: 1px solid rgba(255,255,255,0.1);
}
.owl-item {
	background-color: #fff;
	border: 4px solid #2f3961;
}
.section {
	padding:80px 0;
}
.sboard.sb-metro{ 
	.sb-item {
		background-color: #1b2e50 !important;
		.sb-inner3, .sb-info{
			background-color:#091b3e !important;
		}
	}
}
.sb-hover {
	background-color:#091b3e !important;
}
.filter-items {
	padding: 15px 1px 30px 0 !important;
	text-align: center !important;
}
.filter-label {
	width: 40px !important;
	line-height: 65px !important;
	height: 40px !important;
	border-radius: 40px !important;
	background-color: $dk-blue !important;
	border: 2px solid #fff !important;
}
.sb-loadmore {
	padding:0 !important;
	background-color: transparent !important;
	font-size: 14px !important;
	p{
		border: 2px solid #fff;
		background-color: transparent !important;
		color: #fff;
		line-height: 36px !important;
		font-weight: 400;
		text-shadow: none;
		border-radius: 3px;
		height: 40px !important;
		letter-spacing: 2px;
		font-size: 14px !important;
		transition: all .2s ease-in-out;
		&:hover {
			background-color: #fff !important;
			color:#000 !important;
		}
	}
}
.col-padding-sm {
	padding: 40px;
}
.sm-form-control::-webkit-input-placeholder { color: rgba(255,255,255,0.6) !important; }  /* WebKit, Blink, Edge */
.sm-form-control:-moz-placeholder { color: rgba(255,255,255,0.6)  !important; }  /* Mozilla Firefox 4 to 18 */
.sm-form-control::-moz-placeholder { color: rgba(255,255,255,0.6)  !important; }  /* Mozilla Firefox 19+ */
.sm-form-control:-ms-input-placeholder { color: rgba(255,255,255,0.6)  !important; }  /* Internet Explorer 10-11 */
.sm-form-control::-ms-input-placeholder { color: rgba(255,255,255,0.6)  !important; }  /* Microsoft Edge */


footer {
	.social-icon {
		display:inline-block !important;
		float:none;
	}
}

#footer.dark, .dark #footer {
	background: #090e33;
	background: -moz-linear-gradient(top, #090e33 0%, #0f2c52 100%);
	background: -webkit-gradient(left top, left bottom, color-stop(0%, #090e33), color-stop(100%, #0f2c52));
	background: -webkit-linear-gradient(top, #090e33 0%, #0f2c52 100%);
	background: -o-linear-gradient(top, #090e33 0%, #0f2c52 100%);
	background: -ms-linear-gradient(top, #090e33 0%, #0f2c52 100%);
	background: linear-gradient(to bottom, #090e33 0%, #0f2c52 100%);
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#090e33', endColorstr='#0f2c52', GradientType=0 );
}

#gotoTop {
	border-radius:40px;
	line-height:38px;
}
.gm-style .gm-style-iw {
	color:$dk-blue;
}
.footer-widgets-wrap {
	.dark .form-control:not(.not-dark), .dark .sm-form-control:not(.not-dark) {
		background-color:#fff !important;
	}
}
.filter-items input[type="text"] {
	background: #090e33 !important;
	color:#fff !important;
	font-size:13px !important;
}




@media (max-width: 575.98px){
	.swiper_wrapper:not(.force-full-screen), .swiper_wrapper:not(.force-full-screen):not(.canvas-slider-grid) .swiper-slide {
		height: 240px !important;
	}
	.dark .toggle.toggle-bg .togglet {
		padding:6px 15px !important;
	}
	.togglet {
		h3 {
			width:90%;
			font-size:19px;
			padding-top:20px;
			span {
				display:none;
			}

		}
	}
	.page-section {
		padding: 60px 0 !important;
	}
}
@media (max-width: 767.98px) {
	.heading-block {
		h2 {
			font-size:16px !important;
		}
	}
	.content-wrap {
		padding:50px 0;
	}
	.toggle {
		&.toggle-bg {
			.togglec {
				padding:12px 0;
			}
		}
	}
	.hidden-xs {
		display: none !important;
		min-height:0px;
	}
	.page-section {
		padding: 70px 0 !important;
	}
}
@media (max-width: 991.98px) {
	#header.dark.semi-transparent, #header.transparent-header.dark:not(.sticky-header) #header-wrap {
		background-color: rgba(29,55,103,1) !important;
		border-bottom: 1px solid rgba(255,255,255,0.1);
	}
	#header.dark.transparent-header {
		background-color: rgba(29,55,103,1) !important;
		// border-bottom: 1px solid rgba(255,255,255,0.1);
	}

}


