/* ----------------------------------------------------------------
    Fonts

    Replace your Fonts as necessary
-----------------------------------------------------------------*/


body,
small,
#primary-menu ul ul li > a,
.wp-caption,
.feature-box.fbox-center.fbox-italic p,
.skills li .progress-percent .counter,
.nav-tree ul ul a,
.font-body,
.entry-meta li,
.entry-link span,
.entry blockquote p,
.more-link,
.comment-content .comment-author span,
.button.button-desc span,
.testi-content p,
.team-title span,
.before-heading,
.wedding-head .first-name span,
.wedding-head .last-name span,
.font-secondary { font-family: '<PERSON><PERSON>', sans-serif; }

h1,
h2,
h3,
h4,
h5,
h6,
#logo,
#primary-menu ul li > a,
#primary-menu ul li .mega-menu-content.style-2 ul.mega-menu-column > li.mega-menu-title > a,
#top-search form input,
.entry-link,
.entry.entry-date-section span,
.button.button-desc,
.counter,
label,
.nav-tree li a,
.wedding-head .first-name,
.wedding-head .last-name,
.font-primary { font-family: 'Roboto Condensed', sans-serif; }

/*! Generated by Font Squirrel (https://www.fontsquirrel.com) on September 27, 2018 */



@font-face {
    font-family: 'bebas_neuebold';
    src: url('fonts/bebasneue_bold-webfont.woff2') format('woff2'),
         url('fonts/bebasneue_bold-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}



@font-face {
    font-family: 'bebas_neuebook';
    src: url('fonts/bebasneue_book-webfont.woff2') format('woff2'),
         url('fonts/bebasneue_book-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}



@font-face {
    font-family: 'bebas_neuelight';
    src: url('fonts/bebasneue_light-webfont.woff2') format('woff2'),
         url('fonts/bebasneue_light-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'bebas_neueregular';
    src: url('fonts/bebasneue_regular-webfont.woff2') format('woff2'),
         url('fonts/bebasneue_regular-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'bebas_neuethin';
    src: url('fonts/bebasneue_thin-webfont.woff2') format('woff2'),
         url('fonts/bebasneue_thin-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}
